{"name": "example-counter", "version": "2.0.2", "private": true, "type": "module", "workspaces": ["counter-cli", "contract"], "devDependencies": {"@eslint/js": "^9.34.0", "@types/node": "^24.3.0", "@types/ws": "^8.18.1", "@typescript-eslint/eslint-plugin": "^8.41.0", "@typescript-eslint/parser": "^8.41.0", "eslint": "^9.34.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "testcontainers": "^11.5.1", "ts-node": "^10.9.2", "tsx": "^4.20.5", "typescript": "^5.9.2", "vitest": "^3.2.4"}, "dependencies": {"@midnight-ntwrk/compact-runtime": "^0.8.1", "@midnight-ntwrk/ledger": "^4.0.0", "@midnight-ntwrk/midnight-js-contracts": "2.0.2", "@midnight-ntwrk/midnight-js-http-client-proof-provider": "2.0.2", "@midnight-ntwrk/midnight-js-indexer-public-data-provider": "2.0.2", "@midnight-ntwrk/midnight-js-level-private-state-provider": "2.0.2", "@midnight-ntwrk/midnight-js-node-zk-config-provider": "2.0.2", "@midnight-ntwrk/midnight-js-types": "2.0.2", "@midnight-ntwrk/wallet": "5.0.0", "@midnight-ntwrk/wallet-api": "5.0.0", "@midnight-ntwrk/zswap": "^4.0.0", "pino": "^9.9.0", "pino-pretty": "^13.1.1", "ws": "^8.18.3"}, "packageManager": "yarn@4.9.4"}