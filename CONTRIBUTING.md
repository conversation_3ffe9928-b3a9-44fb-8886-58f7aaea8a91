# Contributing

We welcome your contributions to the Midnight network! By contributing, you'll play a vital role in shaping the future of a blockchain focused on data privacy.

## Getting Started

* **Review Existing Contributions and Issues:** Before submitting, please check if a similar issue or feature request already exists by searching our issue tracker.
* **Understand the Project:** Familiarize yourself with Midnight's architecture, technology, and coding standards. You can find relevant information in our litepaper. 
* **Set up Your Development Environment:** Ensure you have the necessary tools and dependencies installed. See our developer [documentation](https://docs.midnight.network/) for detailed instructions. 

## Submitting Issues

Use one of the [templates] to submit an issue to the Project Board. The Midnight team or a community member will address it if it's relevant.
Ensure the title is a clear summary of the requirement and provides enough context.

**Issue Types:**

* **Bug Report:** Provide detailed information about the issue, including steps to reproduce it, expected behavior, and actual behavior, screenshots, or any other relevant information.
* **Documentation Improvement:** Clearly describe the improvement requested for existing content and/or raise missing areas of documentation and provide details for what should be included.  
* **Feature Request:** Clearly describe your feature, its benefits, and most importantly, the expected outcome. This helps us analyze the proposed solution and develop alternatives.
* **Enhancement:** (WIP)

## Code Contribution Process

* **Pull Requests:** Code contributions are submitted via Pull Requests.
* **Fork the Repository:** Create your own fork of the Midnight repository.
* **Create a Branch:** Make your changes in a separate branch.
* **Follow Coding Standards:** Adhere to the coding style guides specified in our documentation.
* **Write Tests:** Include unit tests and integration tests to cover your changes.
* **Commit Messages:** Write clear and concise commit messages.
* **Submit Pull Request:** Submit your pull request to the appropriate branch in the main repository.
* **Code Review:** All pull requests undergo code review by project maintainers. Be prepared to address feedback from reviewers.

## Requirements for Acceptable Contributions:

* **Coding Standards:** Code must adhere to the coding style guides defined in our documentation
* **Testing:** New functionality must include corresponding unit tests and integration tests.
* **Documentation:** Code changes should be accompanied by proposed relevant documentation updates.
* **License:** All contributions must be compatible with the project's license.

## Support and Communication:

Ask anything about Midnight! We're here to help. Connect with us on [Discord](https://discord.com/invite/midnightnetwork), [Telegram](https://t.me/Midnight_Network_Official), and [X](https://x.com/MidnightNtwrk) and Join the Community to stay updated and engage with other Midnight enthusiasts.

We appreciate your contributions!
