{"name": "@midnight-ntwrk/counter-contract", "version": "0.1.0", "license": "Apache-2.0", "private": true, "type": "module", "main": "dist/index.js", "module": "dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/index.js", "import": "./dist/index.js", "default": "./dist/index.js"}}, "scripts": {"compact": "compactc.bin src/counter.compact src/managed/counter", "test": "vitest run", "test:compile": "npm run compact && vitest run", "build": "rm -rf dist && tsc --project tsconfig.build.json && cp -Rf ./src/managed ./dist/managed && cp ./src/counter.compact ./dist", "lint": "eslint src", "typecheck": "tsc -p tsconfig.json --noEmit"}, "devDependencies": {"typescript": "^5.9.2"}}