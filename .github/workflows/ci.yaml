name: CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  install-and-test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v5

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Install dependencies
        run: npm ci

      - name: Install compactc
        env:
          COMPACT_VERSION: 0.25.0
        run: |
          curl --proto '=https' --tlsv1.2 -LsSf https://github.com/midnightntwrk/compact/releases/latest/download/compact-installer.sh | sh
          compact update $COMPACT_VERSION
          compact compile --version

      - name: Compile and test contract
        working-directory: contract
        run: |
          npm run lint
          npm run compact
          npm run typecheck
          npm run build
          npm run test

      - name: Compile counter-cli
        working-directory: counter-cli
        run: |
          npm run lint
          npm run typecheck
          npm run build
          npm run test-api
