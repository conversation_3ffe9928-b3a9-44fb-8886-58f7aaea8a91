version: 2
updates:
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
    labels:
      - 'dependencies'
      - 'github-actions'

  - package-ecosystem: "npm"
    directory: "/"
    schedule:
      interval: "daily"
    open-pull-requests-limit: 30
    labels:
      - 'dependencies'
      - 'npm'
    versioning-strategy: auto
      
  - package-ecosystem: "npm"
    directory: "/contract"
    schedule:
      interval: "daily"
    labels:
      - 'dependencies'
      - 'npm'
      - 'contract'

  - package-ecosystem: "npm"
    directory: "/counter-cli"
    schedule:
      interval: "daily"
    labels:
      - 'dependencies'
      - 'npm'
      - 'counter-cli'
